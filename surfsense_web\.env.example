NEXT_PUBLIC_FASTAPI_BACKEND_URL=http://localhost:8000
NEXT_PUBLIC_FASTAPI_BACKEND_AUTH_TYPE=LOCAL or GOOGLE
NEXT_PUBLIC_ETL_SERVICE=UNSTRUCTURED or LLAMACLOUD

# Rendering Configuration
# Set to 'true', '1', 'yes', or 'on' to disable server-side rendering (SSR)
# and use client-side rendering (CSR) only. Default is SSR enabled.
#
# When to use CSR mode:
# - Deploying to static hosting (Netlify, Vercel static export, etc.)
# - Debugging hydration issues
# - Reducing server load for highly interactive applications
# - Working with client-only libraries that don't support SSR
#
# Performance considerations:
# - SSR: Better SEO, faster initial page load, better perceived performance
# - CSR: Faster navigation after initial load, reduced server load
#
# NEXT_PUBLIC_DISABLE_SSR=false