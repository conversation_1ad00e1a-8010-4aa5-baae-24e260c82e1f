# Development Dockerfile for CSR-only Next.js application
FROM node:20-alpine

WORKDIR /app

# Install pnpm
RUN npm install -g pnpm

# Copy package files
COPY package.json pnpm-lock.yaml ./

# Copy config files needed for postinstall
COPY source.config.ts ./
COPY content ./content

# Install dependencies with --ignore-scripts to skip postinstall
RUN pnpm install --ignore-scripts

# Now run the postinstall script manually
RUN pnpm fumadocs-mdx

# Copy source code
COPY . .

EXPOSE 3000

# Start Next.js in development mode
CMD ["pnpm", "dev"]
