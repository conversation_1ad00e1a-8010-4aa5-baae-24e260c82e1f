version: '3.8'

services:
  frontend:
    build:
      context: ./surfsense_web
      dockerfile: Dockerfile
    ports:
      - "${FRONTEND_PORT:-3000}:80"
    depends_on:
      - backend
    environment:
      - NEXT_PUBLIC_API_URL=${NEXT_PUBLIC_API_URL:-http://backend:8000}
      - NEXT_PUBLIC_FASTAPI_BACKEND_URL=${NEXT_PUBLIC_API_URL:-http://backend:8000}
      - NEXT_PUBLIC_FASTAPI_BACKEND_AUTH_TYPE=${NEXT_PUBLIC_FASTAPI_BACKEND_AUTH_TYPE:-LOCAL}
      - NEXT_PUBLIC_ETL_SERVICE=${NEXT_PUBLIC_ETL_SERVICE:-UNSTRUCTURED}
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  backend:
    image: ghcr.io/modsetter/surfsense_backend:latest
    ports:
      - "${BACKEND_PORT:-8000}:8000"
    volumes:
      - ./surfsense_backend:/app
    depends_on:
      - db
    env_file:
      - ./surfsense_backend/.env
    environment:
      - DATABASE_URL=postgresql+asyncpg://${POSTGRES_USER:-postgres}:${POSTGRES_PASSWORD:-postgres}@db:5432/${POSTGRES_DB:-surfsense}
      - PYTHONPATH=/app
      - UVICORN_LOOP=asyncio
      - UNSTRUCTURED_HAS_PATCHED_LOOP=1
