#!/usr/bin/env node

/**
 * Test script for validating SSR/CSR configuration
 * Run this script to validate your rendering mode setup
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function checkEnvironmentFile() {
  log('\n📋 Checking environment configuration...', 'cyan');
  
  const envFiles = ['.env', '.env.local', '.env.example'];
  let found = false;
  
  for (const file of envFiles) {
    const filePath = path.join(process.cwd(), file);
    if (fs.existsSync(filePath)) {
      const content = fs.readFileSync(filePath, 'utf8');
      if (content.includes('NEXT_PUBLIC_DISABLE_SSR')) {
        log(`✅ Found NEXT_PUBLIC_DISABLE_SSR in ${file}`, 'green');
        found = true;
        
        // Extract the value
        const match = content.match(/NEXT_PUBLIC_DISABLE_SSR\s*=\s*(.+)/);
        if (match) {
          const value = match[1].trim();
          log(`   Value: ${value}`, 'blue');
          
          if (['true', '1', 'yes', 'on'].includes(value.toLowerCase())) {
            log('   Mode: CSR (Client-Side Rendering)', 'yellow');
          } else {
            log('   Mode: SSR (Server-Side Rendering)', 'green');
          }
        }
      }
    }
  }
  
  if (!found) {
    log('⚠️  NEXT_PUBLIC_DISABLE_SSR not found in environment files', 'yellow');
    log('   This means SSR mode is enabled by default', 'blue');
  }
}

function checkConfigFiles() {
  log('\n📋 Checking configuration files...', 'cyan');
  
  const configFiles = [
    'next.config.ts',
    'next.config.js',
    'lib/config.ts',
    'components/rendering/index.ts'
  ];
  
  for (const file of configFiles) {
    const filePath = path.join(process.cwd(), file);
    if (fs.existsSync(filePath)) {
      log(`✅ Found ${file}`, 'green');
    } else {
      log(`❌ Missing ${file}`, 'red');
    }
  }
}

function checkDockerConfiguration() {
  log('\n📋 Checking Docker configuration...', 'cyan');
  
  const dockerFiles = [
    'Dockerfile',
    'Dockerfile.csr',
    'docker-compose.yml',
    'docker-compose.csr.yml',
    'docker-compose.override.yml'
  ];
  
  for (const file of dockerFiles) {
    const filePath = path.join(process.cwd(), file);
    if (fs.existsSync(filePath)) {
      log(`✅ Found ${file}`, 'green');
      
      // Check if it contains SSR configuration
      const content = fs.readFileSync(filePath, 'utf8');
      if (content.includes('NEXT_PUBLIC_DISABLE_SSR')) {
        log(`   Contains SSR configuration`, 'blue');
      }
    } else {
      log(`⚠️  ${file} not found`, 'yellow');
    }
  }
}

function runBuildTest(mode) {
  log(`\n🔨 Testing ${mode.toUpperCase()} build...`, 'cyan');
  
  try {
    const env = mode === 'csr' ? 'NEXT_PUBLIC_DISABLE_SSR=true' : 'NEXT_PUBLIC_DISABLE_SSR=false';
    
    log(`   Setting environment: ${env}`, 'blue');
    
    // Run build with timeout
    const buildCommand = `${env} npm run build`;
    log(`   Running: ${buildCommand}`, 'blue');
    
    execSync(buildCommand, { 
      stdio: 'pipe',
      timeout: 120000, // 2 minutes timeout
      env: { ...process.env, NEXT_PUBLIC_DISABLE_SSR: mode === 'csr' ? 'true' : 'false' }
    });
    
    log(`✅ ${mode.toUpperCase()} build successful`, 'green');
    return true;
  } catch (error) {
    log(`❌ ${mode.toUpperCase()} build failed`, 'red');
    log(`   Error: ${error.message}`, 'red');
    return false;
  }
}

function generateTestReport() {
  log('\n📊 Generating test report...', 'cyan');
  
  const report = {
    timestamp: new Date().toISOString(),
    environment: process.env.NODE_ENV || 'development',
    tests: {
      environmentConfig: 'checked',
      configFiles: 'checked',
      dockerConfig: 'checked',
    },
    recommendations: []
  };
  
  // Add recommendations based on findings
  if (!fs.existsSync(path.join(process.cwd(), '.env'))) {
    report.recommendations.push('Create a .env file with NEXT_PUBLIC_DISABLE_SSR configuration');
  }
  
  if (!fs.existsSync(path.join(process.cwd(), 'Dockerfile.csr'))) {
    report.recommendations.push('Consider creating Dockerfile.csr for optimized CSR deployments');
  }
  
  const reportPath = path.join(process.cwd(), 'rendering-test-report.json');
  fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
  
  log(`✅ Test report saved to ${reportPath}`, 'green');
}

function showUsageExamples() {
  log('\n💡 Usage Examples:', 'magenta');
  log('');
  log('Development:', 'bright');
  log('  # SSR mode', 'blue');
  log('  npm run dev', 'blue');
  log('');
  log('  # CSR mode', 'blue');
  log('  NEXT_PUBLIC_DISABLE_SSR=true npm run dev', 'blue');
  log('');
  log('Production:', 'bright');
  log('  # SSR deployment', 'blue');
  log('  docker-compose up -d', 'blue');
  log('');
  log('  # CSR deployment', 'blue');
  log('  docker-compose -f docker-compose.csr.yml up -d', 'blue');
  log('');
  log('Testing:', 'bright');
  log('  # Run this test script', 'blue');
  log('  node scripts/test-rendering-modes.js', 'blue');
  log('');
  log('  # Browser console (development)', 'blue');
  log('  window.__SURFSENSE_TESTS__.runAllTests()', 'blue');
}

function main() {
  log('🎨 SurfSense Rendering Mode Test Suite', 'bright');
  log('=====================================', 'bright');
  
  checkEnvironmentFile();
  checkConfigFiles();
  checkDockerConfiguration();
  
  // Only run build tests if explicitly requested
  if (process.argv.includes('--build-test')) {
    const ssrSuccess = runBuildTest('ssr');
    const csrSuccess = runBuildTest('csr');
    
    if (ssrSuccess && csrSuccess) {
      log('\n🎉 All build tests passed!', 'green');
    } else {
      log('\n⚠️  Some build tests failed. Check the output above.', 'yellow');
    }
  } else {
    log('\n💡 To run build tests, use: node scripts/test-rendering-modes.js --build-test', 'blue');
  }
  
  generateTestReport();
  showUsageExamples();
  
  log('\n✨ Test suite completed!', 'bright');
  log('For runtime tests, open your browser console in development mode', 'blue');
  log('and run: window.__SURFSENSE_TESTS__.runAllTests()', 'blue');
}

// Run the main function
if (require.main === module) {
  main();
}
