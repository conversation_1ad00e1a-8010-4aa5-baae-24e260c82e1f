/**
 * Testing utilities for SSR/CSR modes
 * Provides utilities to test rendering modes and validate functionality
 */

import { getConfig, validateConfig, getRenderingContext } from './config';
import { isClient, isServer } from './config';

/**
 * Test suite interface for rendering mode tests
 */
export interface RenderingTestSuite {
  name: string;
  tests: RenderingTest[];
}

export interface RenderingTest {
  name: string;
  description: string;
  test: () => Promise<TestResult> | TestResult;
  expectedInSSR: boolean;
  expectedInCSR: boolean;
}

export interface TestResult {
  passed: boolean;
  message: string;
  details?: any;
}

/**
 * Create a test result
 */
export function createTestResult(passed: boolean, message: string, details?: any): TestResult {
  return { passed, message, details };
}

/**
 * Basic configuration validation tests
 */
export const configurationTests: RenderingTestSuite = {
  name: 'Configuration Tests',
  tests: [
    {
      name: 'Environment Variable Parsing',
      description: 'Test that NEXT_PUBLIC_DISABLE_SSR is parsed correctly',
      expectedInSSR: true,
      expectedInCSR: true,
      test: () => {
        const config = getConfig();
        const envValue = process.env.NEXT_PUBLIC_DISABLE_SSR;
        
        if (envValue === 'true' && !config.disableSSR) {
          return createTestResult(false, 'Environment variable is "true" but SSR is not disabled');
        }
        
        if (!envValue && config.disableSSR) {
          return createTestResult(false, 'Environment variable is not set but SSR is disabled');
        }
        
        return createTestResult(true, 'Environment variable parsing is correct', { envValue, config });
      },
    },
    {
      name: 'Configuration Validation',
      description: 'Test that configuration is valid',
      expectedInSSR: true,
      expectedInCSR: true,
      test: () => {
        const validation = validateConfig();
        
        if (!validation.isValid) {
          return createTestResult(false, 'Configuration validation failed', validation.errors);
        }
        
        return createTestResult(true, 'Configuration is valid');
      },
    },
    {
      name: 'Rendering Mode Consistency',
      description: 'Test that rendering mode is consistent with configuration',
      expectedInSSR: true,
      expectedInCSR: true,
      test: () => {
        const config = getConfig();
        const context = getRenderingContext();
        
        if (config.disableSSR && context.mode !== 'csr') {
          return createTestResult(false, 'SSR is disabled but mode is not CSR', { config, context });
        }
        
        if (!config.disableSSR && context.mode !== 'ssr') {
          return createTestResult(false, 'SSR is enabled but mode is not SSR', { config, context });
        }
        
        return createTestResult(true, 'Rendering mode is consistent');
      },
    },
  ],
};

/**
 * Runtime environment tests
 */
export const runtimeTests: RenderingTestSuite = {
  name: 'Runtime Tests',
  tests: [
    {
      name: 'Server Detection',
      description: 'Test server environment detection',
      expectedInSSR: true,
      expectedInCSR: true,
      test: () => {
        const serverDetected = isServer();
        const clientDetected = isClient();
        
        if (serverDetected && clientDetected) {
          return createTestResult(false, 'Both server and client detected simultaneously');
        }
        
        if (!serverDetected && !clientDetected) {
          return createTestResult(false, 'Neither server nor client detected');
        }
        
        return createTestResult(true, `Environment correctly detected as ${serverDetected ? 'server' : 'client'}`);
      },
    },
    {
      name: 'Window Object Access',
      description: 'Test safe window object access',
      expectedInSSR: true,
      expectedInCSR: true,
      test: () => {
        try {
          const hasWindow = typeof window !== 'undefined';
          const isClientEnv = isClient();
          
          if (hasWindow !== isClientEnv) {
            return createTestResult(false, 'Window detection inconsistent with client detection');
          }
          
          return createTestResult(true, `Window object access is safe (hasWindow: ${hasWindow})`);
        } catch (error) {
          return createTestResult(false, 'Error accessing window object', error);
        }
      },
    },
    {
      name: 'LocalStorage Access',
      description: 'Test safe localStorage access',
      expectedInSSR: false,
      expectedInCSR: true,
      test: () => {
        if (isServer()) {
          return createTestResult(true, 'Skipped on server (expected)');
        }
        
        try {
          const testKey = '__surfsense_test__';
          const testValue = 'test_value';
          
          localStorage.setItem(testKey, testValue);
          const retrieved = localStorage.getItem(testKey);
          localStorage.removeItem(testKey);
          
          if (retrieved !== testValue) {
            return createTestResult(false, 'LocalStorage read/write failed');
          }
          
          return createTestResult(true, 'LocalStorage access is working');
        } catch (error) {
          return createTestResult(false, 'LocalStorage access failed', error);
        }
      },
    },
  ],
};

/**
 * Component rendering tests
 */
export const componentTests: RenderingTestSuite = {
  name: 'Component Tests',
  tests: [
    {
      name: 'CSR Initialization',
      description: 'Test that CSR mode is properly initialized',
      expectedInSSR: false,
      expectedInCSR: true,
      test: () => {
        const config = getConfig();
        
        if (!config.disableSSR) {
          return createTestResult(true, 'Skipped in SSR mode (expected)');
        }
        
        if (isServer()) {
          return createTestResult(true, 'Skipped on server (expected)');
        }
        
        const isInitialized = document.body.hasAttribute('data-csr-initialized');
        const hasCSRClass = document.documentElement.classList.contains('csr-mode');
        
        if (!isInitialized) {
          return createTestResult(false, 'CSR initialization marker not found');
        }
        
        if (!hasCSRClass) {
          return createTestResult(false, 'CSR mode class not applied');
        }
        
        return createTestResult(true, 'CSR mode is properly initialized');
      },
    },
  ],
};

/**
 * Run a single test
 */
export async function runTest(test: RenderingTest): Promise<TestResult & { skipped: boolean }> {
  const config = getConfig();
  const shouldRun = config.disableSSR ? test.expectedInCSR : test.expectedInSSR;
  
  if (!shouldRun) {
    return {
      passed: true,
      message: `Skipped in ${config.renderingMode.toUpperCase()} mode`,
      skipped: true,
    };
  }
  
  try {
    const result = await test.test();
    return { ...result, skipped: false };
  } catch (error) {
    return {
      passed: false,
      message: `Test threw an error: ${error instanceof Error ? error.message : String(error)}`,
      details: error,
      skipped: false,
    };
  }
}

/**
 * Run a test suite
 */
export async function runTestSuite(suite: RenderingTestSuite): Promise<{
  name: string;
  results: Array<TestResult & { name: string; skipped: boolean }>;
  summary: {
    total: number;
    passed: number;
    failed: number;
    skipped: number;
  };
}> {
  const results = [];
  
  for (const test of suite.tests) {
    const result = await runTest(test);
    results.push({ ...result, name: test.name });
  }
  
  const summary = {
    total: results.length,
    passed: results.filter(r => r.passed && !r.skipped).length,
    failed: results.filter(r => !r.passed && !r.skipped).length,
    skipped: results.filter(r => r.skipped).length,
  };
  
  return {
    name: suite.name,
    results,
    summary,
  };
}

/**
 * Run all test suites
 */
export async function runAllTests(): Promise<{
  suites: Array<Awaited<ReturnType<typeof runTestSuite>>>;
  overallSummary: {
    total: number;
    passed: number;
    failed: number;
    skipped: number;
  };
}> {
  const testSuites = [configurationTests, runtimeTests, componentTests];
  const suites = [];
  
  for (const suite of testSuites) {
    const result = await runTestSuite(suite);
    suites.push(result);
  }
  
  const overallSummary = suites.reduce(
    (acc, suite) => ({
      total: acc.total + suite.summary.total,
      passed: acc.passed + suite.summary.passed,
      failed: acc.failed + suite.summary.failed,
      skipped: acc.skipped + suite.summary.skipped,
    }),
    { total: 0, passed: 0, failed: 0, skipped: 0 }
  );
  
  return { suites, overallSummary };
}

/**
 * Log test results to console
 */
export function logTestResults(results: Awaited<ReturnType<typeof runAllTests>>): void {
  console.group('🧪 SurfSense Rendering Mode Tests');
  
  results.suites.forEach(suite => {
    console.group(`📋 ${suite.name}`);
    console.log(`Summary: ${suite.summary.passed}/${suite.summary.total} passed, ${suite.summary.skipped} skipped`);
    
    suite.results.forEach(result => {
      const icon = result.skipped ? '⏭️' : result.passed ? '✅' : '❌';
      console.log(`${icon} ${result.name}: ${result.message}`);
      
      if (result.details && !result.passed) {
        console.log('   Details:', result.details);
      }
    });
    
    console.groupEnd();
  });
  
  console.log(`\n📊 Overall Summary: ${results.overallSummary.passed}/${results.overallSummary.total} passed, ${results.overallSummary.skipped} skipped`);
  
  if (results.overallSummary.failed > 0) {
    console.warn(`⚠️ ${results.overallSummary.failed} tests failed. Check the details above.`);
  } else {
    console.log('🎉 All tests passed!');
  }
  
  console.groupEnd();
}

/**
 * Initialize testing utilities (development only)
 */
export function initializeTestUtils(): void {
  if (process.env.NODE_ENV !== 'development' || isServer()) return;
  
  // Add global test functions
  (window as any).__SURFSENSE_TESTS__ = {
    runTest,
    runTestSuite,
    runAllTests,
    logTestResults,
    configurationTests,
    runtimeTests,
    componentTests,
  };
  
  console.log('🧪 SurfSense test utilities initialized. Use window.__SURFSENSE_TESTS__ for testing.');
}
