/**
 * Rendering utilities for handling SSR/CSR transitions
 * Provides hooks and utilities for conditional rendering based on SSR/CSR mode
 */

'use client';

import { useEffect, useState } from 'react';
import { getConfig, getRenderingContext, isClient, isServer } from './config';

/**
 * Hook to detect if component is mounted (client-side)
 * Useful for preventing hydration mismatches
 */
export function useIsMounted(): boolean {
  const [isMounted, setIsMounted] = useState(false);

  useEffect(() => {
    setIsMounted(true);
  }, []);

  return isMounted;
}

/**
 * Hook to get current rendering configuration
 * Returns stable configuration object
 */
export function useRenderingConfig() {
  const [config, setConfig] = useState(() => {
    // Only get config on client side to avoid hydration issues
    if (isClient()) {
      return getConfig();
    }
    return null;
  });

  useEffect(() => {
    if (isClient()) {
      setConfig(getConfig());
    }
  }, []);

  return config;
}

/**
 * Hook to get current rendering context
 * Provides information about current rendering state
 */
export function useRenderingContext() {
  const [context, setContext] = useState(() => {
    if (isClient()) {
      return getRenderingContext();
    }
    return null;
  });

  useEffect(() => {
    if (isClient()) {
      setContext(getRenderingContext());
      
      // Mark body as CSR initialized if in CSR mode
      const config = getConfig();
      if (config.disableSSR && !document.body.hasAttribute('data-csr-initialized')) {
        document.body.setAttribute('data-csr-initialized', 'true');
      }
    }
  }, []);

  return context;
}

/**
 * Hook for conditional rendering based on SSR/CSR mode
 * Prevents hydration mismatches by only rendering on client when needed
 */
export function useConditionalRender() {
  const isMounted = useIsMounted();
  const config = useRenderingConfig();

  return {
    isMounted,
    config,
    shouldRenderOnServer: config?.renderingMode === 'ssr',
    shouldRenderOnClient: isMounted && config?.renderingMode === 'csr',
    canRender: config?.renderingMode === 'ssr' || (config?.renderingMode === 'csr' && isMounted),
  };
}

/**
 * Utility function to safely execute client-only code
 * Prevents execution during SSR
 */
export function clientOnly<T>(fn: () => T, fallback?: T): T | undefined {
  if (isClient()) {
    try {
      return fn();
    } catch (error) {
      console.warn('Error in client-only code:', error);
      return fallback;
    }
  }
  return fallback;
}

/**
 * Utility function to safely execute server-only code
 * Prevents execution on client side
 */
export function serverOnly<T>(fn: () => T, fallback?: T): T | undefined {
  if (isServer()) {
    try {
      return fn();
    } catch (error) {
      console.warn('Error in server-only code:', error);
      return fallback;
    }
  }
  return fallback;
}

/**
 * Create a safe localStorage wrapper that works in both SSR and CSR
 */
export function createSafeStorage() {
  return {
    getItem: (key: string): string | null => {
      return clientOnly(() => localStorage.getItem(key), null) || null;
    },
    
    setItem: (key: string, value: string): void => {
      clientOnly(() => localStorage.setItem(key, value));
    },
    
    removeItem: (key: string): void => {
      clientOnly(() => localStorage.removeItem(key));
    },
    
    clear: (): void => {
      clientOnly(() => localStorage.clear());
    },
  };
}

/**
 * Safe storage instance
 */
export const safeStorage = createSafeStorage();

/**
 * Utility to detect if we're in development mode
 */
export function isDevelopment(): boolean {
  return process.env.NODE_ENV === 'development';
}

/**
 * Utility to log rendering mode information (development only)
 */
export function logRenderingInfo(): void {
  if (isDevelopment() && isClient()) {
    const config = getConfig();
    const context = getRenderingContext();
    
    console.group('🎨 SurfSense Rendering Info');
    console.log('Mode:', config.renderingMode.toUpperCase());
    console.log('Environment:', context.environment);
    console.log('Is Hydrating:', context.isHydrating);
    console.log('Config:', config);
    console.groupEnd();
  }
}

/**
 * Initialize client-side rendering mode
 * Should be called once when the app starts in CSR mode
 */
export function initializeCSRMode(): void {
  if (isClient()) {
    const config = getConfig();
    
    if (config.disableSSR) {
      // Mark body as CSR initialized
      document.body.setAttribute('data-csr-initialized', 'true');
      
      // Add CSR mode class for styling
      document.documentElement.classList.add('csr-mode');
      
      // Log info in development
      if (isDevelopment()) {
        logRenderingInfo();
      }
    }
  }
}
