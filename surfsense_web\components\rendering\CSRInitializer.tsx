/**
 * CSR Initializer Component
 * Handles client-side initialization when SSR is disabled
 */

'use client';

import { useEffect } from 'react';
import { initializeCSRMode, logRenderingInfo } from '@/lib/rendering-utils';
import { getConfig } from '@/lib/config';
import { initializeDebugUtils } from '@/lib/debug-utils';
import { initializeTestUtils } from '@/lib/test-utils';

export function CSRInitializer() {
  useEffect(() => {
    const config = getConfig();

    if (config.disableSSR) {
      // Initialize CSR mode
      initializeCSRMode();

      // Log rendering info in development
      if (process.env.NODE_ENV === 'development') {
        logRenderingInfo();
      }
    }

    // Initialize debug and test utilities in development
    if (process.env.NODE_ENV === 'development') {
      initializeDebugUtils();
      initializeTestUtils();
    }
  }, []);

  // This component doesn't render anything
  return null;
}
