/**
 * Configuration utilities for SurfSense application
 * Handles SSR/CSR mode detection and application settings
 */

export type RenderingMode = 'ssr' | 'csr';

export interface AppConfig {
  /** Whether server-side rendering is disabled */
  disableSSR: boolean;
  /** Current rendering mode */
  renderingMode: RenderingMode;
  /** Backend API URL */
  backendUrl: string;
  /** Backend authentication type */
  authType: string;
  /** ETL service configuration */
  etlService: string;
}

/**
 * Environment variable keys used by the application
 */
export const ENV_KEYS = {
  DISABLE_SSR: 'NEXT_PUBLIC_DISABLE_SSR',
  BACKEND_URL: 'NEXT_PUBLIC_FASTAPI_BACKEND_URL',
  AUTH_TYPE: 'NEXT_PUBLIC_FASTAPI_BACKEND_AUTH_TYPE',
  ETL_SERVICE: 'NEXT_PUBLIC_ETL_SERVICE',
} as const;

/**
 * Default configuration values
 */
export const DEFAULT_CONFIG: AppConfig = {
  disableSSR: false,
  renderingMode: 'ssr',
  backendUrl: 'http://localhost:8000',
  authType: 'LOCAL',
  etlService: 'UNSTRUCTURED',
};

/**
 * Parse boolean value from environment variable
 * Supports: 'true', '1', 'yes', 'on' (case insensitive) as true
 * Everything else is false
 */
function parseBooleanEnv(value: string | undefined): boolean {
  if (!value) return false;
  return ['true', '1', 'yes', 'on'].includes(value.toLowerCase());
}

/**
 * Get configuration from environment variables
 */
export function getConfig(): AppConfig {
  const disableSSR = parseBooleanEnv(process.env[ENV_KEYS.DISABLE_SSR]);
  
  return {
    disableSSR,
    renderingMode: disableSSR ? 'csr' : 'ssr',
    backendUrl: process.env[ENV_KEYS.BACKEND_URL] || DEFAULT_CONFIG.backendUrl,
    authType: process.env[ENV_KEYS.AUTH_TYPE] || DEFAULT_CONFIG.authType,
    etlService: process.env[ENV_KEYS.ETL_SERVICE] || DEFAULT_CONFIG.etlService,
  };
}

/**
 * Check if we're running in server-side rendering mode
 */
export function isSSRMode(): boolean {
  return !getConfig().disableSSR;
}

/**
 * Check if we're running in client-side rendering mode
 */
export function isCSRMode(): boolean {
  return getConfig().disableSSR;
}

/**
 * Check if we're currently running on the server
 */
export function isServer(): boolean {
  return typeof window === 'undefined';
}

/**
 * Check if we're currently running on the client
 */
export function isClient(): boolean {
  return typeof window !== 'undefined';
}

/**
 * Get the current rendering context
 */
export function getRenderingContext(): {
  mode: RenderingMode;
  environment: 'server' | 'client';
  isHydrating: boolean;
} {
  const config = getConfig();
  const environment = isServer() ? 'server' : 'client';
  
  // In CSR mode, we're hydrating if we're on the client but SSR was initially attempted
  const isHydrating = config.renderingMode === 'csr' && environment === 'client' && 
                     typeof window !== 'undefined' && !window.document.body.hasAttribute('data-csr-initialized');
  
  return {
    mode: config.renderingMode,
    environment,
    isHydrating,
  };
}

/**
 * Validate the current configuration
 */
export function validateConfig(): { isValid: boolean; errors: string[] } {
  const config = getConfig();
  const errors: string[] = [];
  
  // Validate backend URL
  if (!config.backendUrl) {
    errors.push('Backend URL is required');
  } else {
    try {
      new URL(config.backendUrl);
    } catch {
      errors.push('Backend URL must be a valid URL');
    }
  }
  
  // Validate auth type
  if (!['LOCAL', 'GOOGLE'].includes(config.authType)) {
    errors.push('Auth type must be either LOCAL or GOOGLE');
  }
  
  // Validate ETL service
  if (!['UNSTRUCTURED', 'LLAMACLOUD'].includes(config.etlService)) {
    errors.push('ETL service must be either UNSTRUCTURED or LLAMACLOUD');
  }
  
  return {
    isValid: errors.length === 0,
    errors,
  };
}

/**
 * Get debug information about the current configuration
 */
export function getConfigDebugInfo(): Record<string, any> {
  const config = getConfig();
  const context = getRenderingContext();
  const validation = validateConfig();
  
  return {
    config,
    context,
    validation,
    environment: {
      nodeEnv: process.env.NODE_ENV,
      isProduction: process.env.NODE_ENV === 'production',
      isDevelopment: process.env.NODE_ENV === 'development',
    },
    environmentVariables: {
      [ENV_KEYS.DISABLE_SSR]: process.env[ENV_KEYS.DISABLE_SSR],
      [ENV_KEYS.BACKEND_URL]: process.env[ENV_KEYS.BACKEND_URL],
      [ENV_KEYS.AUTH_TYPE]: process.env[ENV_KEYS.AUTH_TYPE],
      [ENV_KEYS.ETL_SERVICE]: process.env[ENV_KEYS.ETL_SERVICE],
    },
  };
}
