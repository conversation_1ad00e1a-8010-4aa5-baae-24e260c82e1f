# SurfSense SSR/CSR Configuration Guide

This guide explains how to configure and use the Server-Side Rendering (SSR) and Client-Side Rendering (CSR) modes in SurfSense.

## Overview

SurfSense supports both SSR and CSR rendering modes to provide flexibility for different deployment scenarios and performance requirements.

- **SSR (Server-Side Rendering)**: Default mode. Pages are rendered on the server before being sent to the client.
- **CSR (Client-Side Rendering)**: Optional mode. Pages are rendered entirely on the client side.

## Configuration

### Environment Variable

The rendering mode is controlled by the `NEXT_PUBLIC_DISABLE_SSR` environment variable:

```bash
# Enable SSR (default)
NEXT_PUBLIC_DISABLE_SSR=false

# Enable CSR
NEXT_PUBLIC_DISABLE_SSR=true
```

Supported values for enabling CSR: `true`, `1`, `yes`, `on` (case insensitive)

### Configuration Files

1. **Main environment file** (`.env`):
```bash
NEXT_PUBLIC_DISABLE_SSR=false
```

2. **Web application environment** (`surfsense_web/.env`):
```bash
NEXT_PUBLIC_DISABLE_SSR=false
```

## When to Use Each Mode

### Use SSR (Default) When:
- SEO is important
- You need faster initial page loads
- You want better perceived performance
- You're deploying to a server environment
- You need server-side data fetching

### Use CSR When:
- Deploying to static hosting (Netlify, Vercel static export, etc.)
- You have highly interactive applications
- You want to reduce server load
- You're debugging hydration issues
- You're working with client-only libraries that don't support SSR
- You need faster navigation after initial load

## Performance Implications

### SSR Performance Characteristics:
- ✅ Faster Time to First Contentful Paint (FCP)
- ✅ Better SEO and social media sharing
- ✅ Works without JavaScript
- ❌ Higher server resource usage
- ❌ Slower Time to Interactive (TTI)

### CSR Performance Characteristics:
- ✅ Faster navigation after initial load
- ✅ Reduced server load
- ✅ Better for highly interactive apps
- ❌ Slower initial page load
- ❌ Poor SEO without additional configuration
- ❌ Requires JavaScript to function

## Deployment Options

### Standard SSR Deployment

```bash
# Using Docker Compose
docker-compose up -d

# Using environment variables
NEXT_PUBLIC_DISABLE_SSR=false docker-compose up -d
```

### CSR Deployment

```bash
# Using the CSR-specific Docker Compose file
docker-compose -f docker-compose.csr.yml up -d

# Using environment variables with standard compose
NEXT_PUBLIC_DISABLE_SSR=true docker-compose up -d
```

### Static Export (CSR Only)

For static hosting, you can export the application:

```bash
# Set CSR mode
export NEXT_PUBLIC_DISABLE_SSR=true

# Build and export
cd surfsense_web
pnpm build
pnpm export
```

## Development

### Running in Development

```bash
# SSR mode (default)
cd surfsense_web
pnpm dev

# CSR mode
cd surfsense_web
NEXT_PUBLIC_DISABLE_SSR=true pnpm dev
```

### Debug Tools

In development mode, several debug tools are available:

1. **Rendering Mode Indicator**: Shows current mode in bottom-right corner
2. **Debug Panel**: Press `Ctrl+Shift+D` to toggle debug information
3. **Console Logging**: Detailed rendering information in browser console
4. **Global Debug Object**: Access `window.__SURFSENSE_DEBUG__` for debugging functions

## Troubleshooting

### Common Issues

#### 1. Hydration Mismatches
**Symptoms**: Console errors about hydration, content flickering
**Solution**: Use the provided conditional rendering components

```tsx
import { ConditionalRenderer } from '@/components/rendering';

function MyComponent() {
  return (
    <ConditionalRenderer fallback={<div>Loading...</div>}>
      <YourContent />
    </ConditionalRenderer>
  );
}
```

#### 2. Client-Only Code Running on Server
**Symptoms**: `window is not defined` errors
**Solution**: Use client-only utilities

```tsx
import { clientOnly } from '@/components/rendering';

const data = clientOnly(() => localStorage.getItem('key'), null);
```

#### 3. Server-Only Code Running on Client
**Symptoms**: Server-specific APIs failing on client
**Solution**: Use server-only utilities

```tsx
import { serverOnly } from '@/components/rendering';

const serverData = serverOnly(() => process.env.SECRET_KEY, null);
```

### Validation

Use the built-in validation to check your configuration:

```tsx
import { validateConfig } from '@/lib/config';

const validation = validateConfig();
if (!validation.isValid) {
  console.error('Configuration errors:', validation.errors);
}
```

### Performance Monitoring

Monitor rendering performance in development:

```tsx
import { monitorRenderingPerformance } from '@/lib/debug-utils';

// Call once in your app initialization
monitorRenderingPerformance();
```

## Migration Guide

### From SSR to CSR

1. Set the environment variable:
```bash
NEXT_PUBLIC_DISABLE_SSR=true
```

2. Test your application thoroughly
3. Update any server-dependent code
4. Consider SEO implications

### From CSR to SSR

1. Remove or set the environment variable:
```bash
NEXT_PUBLIC_DISABLE_SSR=false
```

2. Ensure server environment is properly configured
3. Test server-side rendering functionality
4. Update deployment configuration

## Best Practices

1. **Use Conditional Rendering**: Always use the provided rendering components for dynamic content
2. **Test Both Modes**: Ensure your application works in both SSR and CSR modes
3. **Monitor Performance**: Use the debug tools to monitor performance implications
4. **Validate Configuration**: Use the validation utilities to ensure proper setup
5. **Document Mode Choice**: Document why you chose a specific rendering mode for your deployment

## API Reference

### Configuration Functions
- `getConfig()`: Get current configuration
- `isSSRMode()`: Check if SSR is enabled
- `isCSRMode()`: Check if CSR is enabled
- `validateConfig()`: Validate current configuration

### Rendering Components
- `ConditionalRenderer`: Main conditional rendering component
- `ClientOnlyRenderer`: Render only on client
- `ServerSafeRenderer`: Safe for both server and client
- `LazyRenderer`: Lazy loading with Suspense

### Debug Utilities
- `logDebugInfo()`: Log debug information
- `collectDebugInfo()`: Collect debug data
- `validateRenderingMode()`: Validate rendering consistency

For more detailed API documentation, see the TypeScript definitions in the source code.

## Quick Reference

### Environment Variables
```bash
# SSR Mode (default)
NEXT_PUBLIC_DISABLE_SSR=false

# CSR Mode
NEXT_PUBLIC_DISABLE_SSR=true
```

### Docker Commands
```bash
# SSR deployment
docker-compose up -d

# CSR deployment
docker-compose -f docker-compose.csr.yml up -d

# With environment override
NEXT_PUBLIC_DISABLE_SSR=true docker-compose up -d
```

### Development Commands
```bash
# SSR development
pnpm dev

# CSR development
NEXT_PUBLIC_DISABLE_SSR=true pnpm dev
```

### Debug Shortcuts
- `Ctrl+Shift+D`: Toggle debug panel
- `window.__SURFSENSE_DEBUG__`: Access debug functions
- Check browser console for rendering information

### Component Usage
```tsx
// Conditional rendering
import { ConditionalRenderer } from '@/components/rendering';

// Client-only content
import { ClientOnlyRenderer } from '@/components/rendering';

// Safe storage access
import { safeStorage } from '@/components/rendering';
```
