# Docker Compose configuration for CSR (Client-Side Rendering) mode
# Use this configuration when you want to run the application in CSR mode

version: '3.8'

services:
  frontend:
    build:
      context: ./surfsense_web
      dockerfile: Dockerfile.csr
      args:
        - NEXT_PUBLIC_DISABLE_SSR=true
    ports:
      - "${FRONTEND_PORT:-3000}:3000"
    depends_on:
      - backend
    environment:
      - NEXT_PUBLIC_API_URL=${NEXT_PUBLIC_API_URL:-http://backend:8000}
      - NEXT_PUBLIC_DISABLE_SSR=true
      - NEXT_PUBLIC_FASTAPI_BACKEND_URL=${NEXT_PUBLIC_API_URL:-http://backend:8000}
      - NEXT_PUBLIC_FASTAPI_BACKEND_AUTH_TYPE=${NEXT_PUBLIC_FASTAPI_BACKEND_AUTH_TYPE:-LOCAL}
      - NEXT_PUBLIC_ETL_SERVICE=${NEXT_PUBLIC_ETL_SERVICE:-UNSTRUCTURED}
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000"]
      interval: 30s
      timeout: 10s
      retries: 3

  backend:
    image: ghcr.io/modsetter/surfsense_backend:latest
    ports:
      - "${BACKEND_PORT:-8000}:8000"
    depends_on:
      - db
    env_file:
      - ./surfsense_backend/.env
    environment:
      - DATABASE_URL=postgresql+asyncpg://${POSTGRES_USER:-postgres}:${POSTGRES_PASSWORD:-postgres}@db:5432/${POSTGRES_DB:-surfsense}
      - PYTHONPATH=/app
      - UVICORN_LOOP=asyncio
      - UNSTRUCTURED_HAS_PATCHED_LOOP=1
    restart: unless-stopped

  db:
    image: ankane/pgvector:latest
    ports:
      - "${POSTGRES_PORT:-5432}:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    environment:
      - POSTGRES_USER=${POSTGRES_USER:-postgres}
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD:-postgres}
      - POSTGRES_DB=${POSTGRES_DB:-surfsense}
    restart: unless-stopped

  pgadmin:
    image: dpage/pgadmin4
    ports:
      - "${PGADMIN_PORT:-5050}:80"
    environment:
      - PGADMIN_DEFAULT_EMAIL=${PGADMIN_DEFAULT_EMAIL:-<EMAIL>}
      - PGADMIN_DEFAULT_PASSWORD=${PGADMIN_DEFAULT_PASSWORD:-surfsense}
    volumes:
      - pgadmin_data:/var/lib/pgadmin
    depends_on:
      - db
    restart: unless-stopped

volumes:
  postgres_data:
  pgadmin_data:
