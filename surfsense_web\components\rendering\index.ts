/**
 * Rendering components and utilities
 * Provides components for handling SSR/CSR transitions
 */

export {
  Con<PERSON><PERSON><PERSON><PERSON>,
  ClientOnlyRenderer,
  ServerSafeRenderer,
  LazyRenderer,
  ProgressiveRenderer,
  HydrationSafeWrapper,
  RenderingModeIndicator,
  RenderingErrorBoundary,
} from './ConditionalRenderer';

// Re-export rendering utilities for convenience
export {
  useIsMounted,
  useRenderingConfig,
  useRenderingContext,
  useConditionalRender,
  clientOnly,
  serverOnly,
  safeStorage,
  isDevelopment,
  logRenderingInfo,
  initializeCSRMode,
} from '@/lib/rendering-utils';

export {
  getConfig,
  isSSRMode,
  isCSRMode,
  isServer,
  isClient,
  getRenderingContext,
  validateConfig,
  getConfigDebugInfo,
} from '@/lib/config';
