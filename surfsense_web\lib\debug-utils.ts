/**
 * Debug utilities for rendering mode detection and troubleshooting
 * Provides development tools for understanding SSR/CSR behavior
 */

import { getConfig, getConfigDebugInfo, getRenderingContext, validateConfig } from './config';
import { isClient, isServer } from './config';

/**
 * Debug information interface
 */
export interface DebugInfo {
  timestamp: string;
  renderingMode: 'ssr' | 'csr';
  environment: 'server' | 'client';
  config: ReturnType<typeof getConfig>;
  context: ReturnType<typeof getRenderingContext>;
  validation: ReturnType<typeof validateConfig>;
  performance: {
    navigationStart?: number;
    domContentLoaded?: number;
    loadComplete?: number;
  };
  userAgent?: string;
  url?: string;
}

/**
 * Collect comprehensive debug information
 */
export function collectDebugInfo(): DebugInfo {
  const config = getConfig();
  const context = getRenderingContext();
  const validation = validateConfig();
  
  const debugInfo: DebugInfo = {
    timestamp: new Date().toISOString(),
    renderingMode: config.renderingMode,
    environment: isServer() ? 'server' : 'client',
    config,
    context,
    validation,
    performance: {},
  };

  // Add client-specific information
  if (isClient()) {
    debugInfo.userAgent = navigator.userAgent;
    debugInfo.url = window.location.href;
    
    // Add performance information if available
    if (window.performance && window.performance.timing) {
      const timing = window.performance.timing;
      debugInfo.performance = {
        navigationStart: timing.navigationStart,
        domContentLoaded: timing.domContentLoadedEventEnd - timing.navigationStart,
        loadComplete: timing.loadEventEnd - timing.navigationStart,
      };
    }
  }

  return debugInfo;
}

/**
 * Log debug information to console (development only)
 */
export function logDebugInfo(): void {
  if (process.env.NODE_ENV !== 'development') return;

  const debugInfo = collectDebugInfo();
  
  console.group('🔍 SurfSense Debug Information');
  console.log('Timestamp:', debugInfo.timestamp);
  console.log('Rendering Mode:', debugInfo.renderingMode.toUpperCase());
  console.log('Environment:', debugInfo.environment);
  
  if (debugInfo.url) {
    console.log('URL:', debugInfo.url);
  }
  
  console.group('Configuration');
  console.table(debugInfo.config);
  console.groupEnd();
  
  console.group('Context');
  console.table(debugInfo.context);
  console.groupEnd();
  
  console.group('Validation');
  console.log('Is Valid:', debugInfo.validation.isValid);
  if (debugInfo.validation.errors.length > 0) {
    console.warn('Errors:', debugInfo.validation.errors);
  }
  console.groupEnd();
  
  if (debugInfo.performance.navigationStart) {
    console.group('Performance');
    console.table(debugInfo.performance);
    console.groupEnd();
  }
  
  console.groupEnd();
}

/**
 * Create a debug panel component for development
 */
export function createDebugPanel(): HTMLElement | null {
  if (process.env.NODE_ENV !== 'development' || isServer()) return null;

  const debugInfo = collectDebugInfo();
  
  const panel = document.createElement('div');
  panel.id = 'surfsense-debug-panel';
  panel.style.cssText = `
    position: fixed;
    top: 10px;
    left: 10px;
    background: rgba(0, 0, 0, 0.9);
    color: white;
    padding: 10px;
    border-radius: 5px;
    font-family: monospace;
    font-size: 12px;
    z-index: 10000;
    max-width: 300px;
    display: none;
  `;
  
  panel.innerHTML = `
    <div style="font-weight: bold; margin-bottom: 5px;">SurfSense Debug</div>
    <div>Mode: ${debugInfo.renderingMode.toUpperCase()}</div>
    <div>Env: ${debugInfo.environment}</div>
    <div>Valid: ${debugInfo.validation.isValid ? '✅' : '❌'}</div>
    ${debugInfo.validation.errors.length > 0 ? 
      `<div style="color: #ff6b6b;">Errors: ${debugInfo.validation.errors.length}</div>` : 
      ''
    }
    <div style="margin-top: 5px; font-size: 10px; opacity: 0.7;">
      Press Ctrl+Shift+D to toggle
    </div>
  `;
  
  // Add keyboard shortcut to toggle panel
  document.addEventListener('keydown', (e) => {
    if (e.ctrlKey && e.shiftKey && e.key === 'D') {
      e.preventDefault();
      panel.style.display = panel.style.display === 'none' ? 'block' : 'none';
    }
  });
  
  document.body.appendChild(panel);
  return panel;
}

/**
 * Validate rendering mode consistency
 */
export function validateRenderingMode(): {
  isConsistent: boolean;
  issues: string[];
  recommendations: string[];
} {
  const issues: string[] = [];
  const recommendations: string[] = [];
  
  const config = getConfig();
  const context = getRenderingContext();
  
  // Check for common issues
  if (config.disableSSR && isServer()) {
    issues.push('SSR is disabled but code is running on server');
    recommendations.push('Ensure your deployment supports client-side rendering');
  }
  
  if (!config.disableSSR && isClient() && !document.body.hasAttribute('data-ssr-rendered')) {
    issues.push('SSR is enabled but no SSR markers found');
    recommendations.push('Check if server-side rendering is working correctly');
  }
  
  if (config.disableSSR && context.isHydrating) {
    issues.push('CSR mode is enabled but hydration is occurring');
    recommendations.push('Check for server-side rendering leakage in CSR mode');
  }
  
  // Check environment variables
  const validation = validateConfig();
  if (!validation.isValid) {
    issues.push(...validation.errors);
    recommendations.push('Fix configuration errors in environment variables');
  }
  
  return {
    isConsistent: issues.length === 0,
    issues,
    recommendations,
  };
}

/**
 * Performance monitoring for rendering modes
 */
export function monitorRenderingPerformance(): void {
  if (process.env.NODE_ENV !== 'development' || isServer()) return;

  const config = getConfig();
  const startTime = performance.now();
  
  // Monitor initial render
  const observer = new PerformanceObserver((list) => {
    const entries = list.getEntries();
    
    entries.forEach((entry) => {
      if (entry.entryType === 'navigation') {
        console.log(`🚀 ${config.renderingMode.toUpperCase()} Navigation Performance:`, {
          mode: config.renderingMode,
          domContentLoaded: entry.domContentLoadedEventEnd - entry.domContentLoadedEventStart,
          loadComplete: entry.loadEventEnd - entry.loadEventStart,
          firstPaint: performance.getEntriesByType('paint').find(p => p.name === 'first-paint')?.startTime,
          firstContentfulPaint: performance.getEntriesByType('paint').find(p => p.name === 'first-contentful-paint')?.startTime,
        });
      }
    });
  });
  
  observer.observe({ entryTypes: ['navigation'] });
  
  // Clean up after 10 seconds
  setTimeout(() => observer.disconnect(), 10000);
}

/**
 * Export debug information as JSON
 */
export function exportDebugInfo(): string {
  const debugInfo = collectDebugInfo();
  const validation = validateRenderingMode();
  
  return JSON.stringify({
    ...debugInfo,
    validation,
    exportedAt: new Date().toISOString(),
  }, null, 2);
}

/**
 * Initialize debug utilities
 */
export function initializeDebugUtils(): void {
  if (process.env.NODE_ENV !== 'development') return;

  if (isClient()) {
    // Create debug panel
    createDebugPanel();
    
    // Monitor performance
    monitorRenderingPerformance();
    
    // Add global debug functions
    (window as any).__SURFSENSE_DEBUG__ = {
      collectDebugInfo,
      logDebugInfo,
      validateRenderingMode,
      exportDebugInfo,
      getConfigDebugInfo,
    };
    
    console.log('🔧 SurfSense debug utilities initialized. Use window.__SURFSENSE_DEBUG__ for debugging.');
  }
}
