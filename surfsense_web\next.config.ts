import type { NextConfig } from "next";
import { createMDX } from 'fumadocs-mdx/next';

// Parse boolean environment variable
function parseBooleanEnv(value: string | undefined): boolean {
  if (!value) return false;
  return ['true', '1', 'yes', 'on'].includes(value.toLowerCase());
}

// Check if SSR should be disabled
const disableSSR = parseBooleanEnv(process.env.NEXT_PUBLIC_DISABLE_SSR);

const nextConfig: NextConfig = {
  typescript: {
    ignoreBuildErrors: true,
  },
  eslint: {
    ignoreDuringBuilds: true,
  },

  // Conditional SSR configuration
  ...(disableSSR && {
    // When SSR is disabled, we can optionally enable static export
    // Uncomment the following lines if you want to generate static files
    // output: 'export',
    // trailingSlash: true,
    // images: {
    //   unoptimized: true,
    // },

    // Disable server-side features that won't work in CSR mode
    experimental: {
      // Disable server components when in CSR mode
      serverComponentsExternalPackages: [],
    },
  }),

  // Add environment variable to runtime config for debugging
  env: {
    RENDERING_MODE: disableSSR ? 'csr' : 'ssr',
  },

  // Custom webpack configuration for conditional builds
  webpack: (config, { dev, isServer }) => {
    // Add custom defines for build-time optimizations
    config.plugins = config.plugins || [];

    // Define rendering mode at build time
    const webpack = require('webpack');
    config.plugins.push(
      new webpack.DefinePlugin({
        '__RENDERING_MODE__': JSON.stringify(disableSSR ? 'csr' : 'ssr'),
        '__IS_SSR_DISABLED__': JSON.stringify(disableSSR),
      })
    );

    return config;
  },

  // Custom headers for CSR mode
  ...(disableSSR && {
    async headers() {
      return [
        {
          source: '/(.*)',
          headers: [
            {
              key: 'X-Rendering-Mode',
              value: 'csr',
            },
          ],
        },
      ];
    },
  }),
};

// Wrap the config with createMDX
const withMDX = createMDX({});

// Log configuration in development
if (process.env.NODE_ENV === 'development') {
  console.log('🎨 SurfSense Rendering Configuration:');
  console.log(`   Mode: ${disableSSR ? 'CSR (Client-Side Rendering)' : 'SSR (Server-Side Rendering)'}`);
  console.log(`   Environment: ${process.env.NODE_ENV}`);
  if (disableSSR) {
    console.log('   ⚠️  SSR is disabled. Some features may not work as expected.');
    console.log('   💡 To enable SSR, set NEXT_PUBLIC_DISABLE_SSR=false or remove the variable.');
  }
}

export default withMDX(nextConfig);
