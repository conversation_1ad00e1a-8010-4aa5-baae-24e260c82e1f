import type { NextConfig } from "next";
import { createMDX } from 'fumadocs-mdx/next';

const nextConfig: NextConfig = {
  // CSR-only configuration
  output: 'export',
  trailingSlash: true,
  skipTrailingSlashRedirect: true,

  // Disable server-side features for CSR-only mode
  images: {
    unoptimized: true,
  },

  typescript: {
    ignoreBuildErrors: true,
  },
  eslint: {
    ignoreDuringBuilds: true,
  },

  // Disable server-side features completely
  experimental: {
    serverComponentsExternalPackages: [],
  },

  // Add environment variable to runtime config
  env: {
    RENDERING_MODE: 'csr',
  },

  // Custom webpack configuration for CSR-only builds
  webpack: (config, { dev, isServer }) => {
    // Add custom defines for build-time optimizations
    config.plugins = config.plugins || [];

    // Define rendering mode at build time
    const webpack = require('webpack');
    config.plugins.push(
      new webpack.DefinePlugin({
        '__RENDERING_MODE__': JSON.stringify('csr'),
        '__IS_SSR_DISABLED__': JSON.stringify(true),
      })
    );

    return config;
  },

  // Custom headers for CSR mode
  async headers() {
    return [
      {
        source: '/(.*)',
        headers: [
          {
            key: 'X-Rendering-Mode',
            value: 'csr',
          },
          {
            key: 'Cache-Control',
            value: 'public, max-age=31536000, immutable',
          },
        ],
      },
    ];
  },

  // Disable API routes in static export mode
  // Note: API routes won't work in static export mode
  distDir: '.next',
};

// Wrap the config with createMDX
const withMDX = createMDX({});

// Log configuration in development
if (process.env.NODE_ENV === 'development') {
  console.log('🎨 SurfSense CSR-Only Configuration:');
  console.log('   Mode: CSR (Client-Side Rendering)');
  console.log('   Output: Static Export');
  console.log('   Environment:', process.env.NODE_ENV);
  console.log('   ⚠️  Server-side features are disabled.');
  console.log('   💡 This is a pure SPA configuration.');
}

export default withMDX(nextConfig);
