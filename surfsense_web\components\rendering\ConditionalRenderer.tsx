/**
 * Conditional rendering components for handling SSR/CSR transitions
 * Prevents hydration mismatches and provides smooth transitions between modes
 */

'use client';

import React, { ReactNode, Suspense } from 'react';
import { useConditionalRender, useIsMounted } from '@/lib/rendering-utils';

interface ConditionalRendererProps {
  children: ReactNode;
  fallback?: ReactNode;
  loadingComponent?: ReactNode;
}

/**
 * Main conditional renderer that handles SSR/CSR mode transitions
 * Prevents hydration mismatches by controlling when content is rendered
 */
export function ConditionalRenderer({ 
  children, 
  fallback = null, 
  loadingComponent = <div>Loading...</div> 
}: ConditionalRendererProps) {
  const { canRender, config } = useConditionalRender();

  // Show loading component while determining render mode
  if (!config) {
    return <>{loadingComponent}</>;
  }

  // In SSR mode, render immediately
  if (config.renderingMode === 'ssr') {
    return <>{children}</>;
  }

  // In CSR mode, only render after client-side hydration
  if (canRender) {
    return <>{children}</>;
  }

  // Show fallback while waiting for client-side rendering
  return <>{fallback}</>;
}

/**
 * Client-only renderer - only renders content on the client side
 * Useful for components that should never render during SSR
 */
export function ClientOnlyRenderer({ 
  children, 
  fallback = null 
}: { 
  children: ReactNode; 
  fallback?: ReactNode; 
}) {
  const isMounted = useIsMounted();

  if (!isMounted) {
    return <>{fallback}</>;
  }

  return <>{children}</>;
}

/**
 * Server-safe renderer - renders content that's safe for both SSR and CSR
 * Provides a loading state during client-side hydration
 */
export function ServerSafeRenderer({ 
  children, 
  loadingComponent = <div className="animate-pulse">Loading...</div> 
}: ConditionalRendererProps) {
  const { config } = useConditionalRender();
  const isMounted = useIsMounted();

  // Show loading during initial client-side render in CSR mode
  if (config?.renderingMode === 'csr' && !isMounted) {
    return <>{loadingComponent}</>;
  }

  return <>{children}</>;
}

/**
 * Lazy renderer with Suspense boundary
 * Provides better loading states for dynamic content
 */
export function LazyRenderer({ 
  children, 
  fallback = <div className="flex items-center justify-center p-4">Loading...</div> 
}: ConditionalRendererProps) {
  return (
    <Suspense fallback={fallback}>
      <ConditionalRenderer fallback={fallback}>
        {children}
      </ConditionalRenderer>
    </Suspense>
  );
}

/**
 * Progressive renderer - shows content progressively based on rendering mode
 * Provides different content for SSR and CSR modes
 */
export function ProgressiveRenderer({
  ssrContent,
  csrContent,
  fallback = null,
}: {
  ssrContent: ReactNode;
  csrContent: ReactNode;
  fallback?: ReactNode;
}) {
  const { config, canRender } = useConditionalRender();

  if (!config) {
    return <>{fallback}</>;
  }

  if (config.renderingMode === 'ssr') {
    return <>{ssrContent}</>;
  }

  if (canRender) {
    return <>{csrContent}</>;
  }

  return <>{fallback}</>;
}

/**
 * Hydration-safe wrapper that prevents hydration mismatches
 * Useful for wrapping components that might have different server/client output
 */
export function HydrationSafeWrapper({ 
  children, 
  suppressHydrationWarning = true 
}: { 
  children: ReactNode; 
  suppressHydrationWarning?: boolean; 
}) {
  const isMounted = useIsMounted();

  return (
    <div suppressHydrationWarning={suppressHydrationWarning}>
      {isMounted ? children : null}
    </div>
  );
}

/**
 * Rendering mode indicator (development only)
 * Shows current rendering mode for debugging
 */
export function RenderingModeIndicator() {
  const { config } = useConditionalRender();

  // Only show in development
  if (process.env.NODE_ENV !== 'development' || !config) {
    return null;
  }

  return (
    <div className="fixed bottom-4 right-4 z-50 bg-black text-white px-3 py-1 rounded text-xs font-mono">
      {config.renderingMode.toUpperCase()}
    </div>
  );
}

/**
 * Error boundary for rendering issues
 */
interface RenderingErrorBoundaryState {
  hasError: boolean;
  error?: Error;
}

export class RenderingErrorBoundary extends React.Component<
  { children: ReactNode; fallback?: ReactNode },
  RenderingErrorBoundaryState
> {
  constructor(props: { children: ReactNode; fallback?: ReactNode }) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error): RenderingErrorBoundaryState {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('Rendering error:', error, errorInfo);
  }

  render() {
    if (this.state.hasError) {
      return (
        this.props.fallback || (
          <div className="p-4 border border-red-300 rounded bg-red-50">
            <h3 className="text-red-800 font-semibold">Rendering Error</h3>
            <p className="text-red-600 text-sm mt-1">
              Something went wrong while rendering this component.
            </p>
            {process.env.NODE_ENV === 'development' && this.state.error && (
              <pre className="text-xs mt-2 text-red-700 overflow-auto">
                {this.state.error.message}
              </pre>
            )}
          </div>
        )
      );
    }

    return this.props.children;
  }
}
