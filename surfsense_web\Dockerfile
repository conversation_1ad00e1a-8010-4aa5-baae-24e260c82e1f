# Multi-stage build for CSR-only Next.js application
FROM node:20-alpine AS base

# Install dependencies only when needed
FROM base AS deps
RUN apk add --no-cache libc6-compat
WORKDIR /app

# Install pnpm
RUN npm install -g pnpm

# Copy package files
COPY package.json pnpm-lock.yaml ./
COPY source.config.ts ./
COPY content ./content

# Install dependencies
RUN pnpm install --frozen-lockfile

# Rebuild the source code only when needed
FROM base AS builder
WORKDIR /app
COPY --from=deps /app/node_modules ./node_modules
COPY . .

# Build the application for static export
ENV NODE_ENV=production
RUN pnpm build

# Production image for serving static files
FROM nginx:alpine AS runner
WORKDIR /usr/share/nginx/html

# Copy the static files from builder stage
COPY --from=builder /app/out .

# Copy nginx configuration for SPA
COPY nginx.conf /etc/nginx/nginx.conf

EXPOSE 80

CMD ["nginx", "-g", "daemon off;"]